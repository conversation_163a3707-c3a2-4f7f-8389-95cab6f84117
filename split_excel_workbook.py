import os
import pandas as pd
import glob
import re
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
import warnings

# Suppress warnings
warnings.simplefilter(action="ignore", category=pd.core.common.SettingWithCopyWarning)
pd.io.formats.excel.ExcelFormatter.header_style = None

class ExcelHeaderSplitter:
    def __init__(self):
        self.console = Console()
        
    def print_header(self, title):
        """Print a formatted header"""
        self.console.print(f"\n[bold magenta]{title}[/bold magenta]")
        self.console.print("=" * len(title))
    
    def detect_email_column_by_content(self, df):
        """Detect email column by analyzing actual content for email patterns"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        for col in df.columns:
            # Skip if column is already named Em<PERSON>
            if col == 'Email':
                continue
                
            # Sample first 10 non-null values to check for email pattern
            sample_values = df[col].dropna().head(10).astype(str)
            
            if len(sample_values) == 0:
                continue
                
            # Count how many values match email pattern
            email_matches = sum(1 for val in sample_values if re.match(email_pattern, val.strip()))
            
            # If more than 70% of sampled values are emails, consider this the email column
            if email_matches / len(sample_values) > 0.7:
                return col
        
        return None
    
    def detect_name_column(self, df):
        """Detect which column contains names"""
        name_variations = [
            'name', 'names', 'author name', 'author', 'full name', 
            'firstname', 'first name', 'last name', 'col1'
        ]
        
        for col in df.columns:
            if col.lower().strip() in name_variations:
                return col
        
        # If no exact match, look for partial matches
        for col in df.columns:
            for variation in name_variations:
                if variation in col.lower():
                    return col
        
        return None
    
    def detect_email_column(self, df):
        """Detect which column contains emails by header name"""
        email_variations = ['email', 'emails', 'email address', 'e-mail', 'mail']
        
        for col in df.columns:
            if col.lower().strip() in email_variations:
                return col
        
        # If no exact match, look for partial matches
        for col in df.columns:
            for variation in email_variations:
                if variation in col.lower():
                    return col
        
        return None
    
    def assign_headers(self, df):
        """Assign standard headers to the dataframe"""
        rename_dict = {}
        
        # First try to detect by header names
        name_col = self.detect_name_column(df)
        email_col = self.detect_email_column(df)
        
        # If no email column found by header name, try content detection
        if not email_col:
            email_col = self.detect_email_column_by_content(df)
            if email_col:
                self.console.print(f"[yellow]Detected email column by content analysis: {email_col}[/yellow]")
        
        # If we found an email column, rename it
        if email_col and email_col != 'Email':
            rename_dict[email_col] = 'Email'
        
        # If we found a name column, rename it
        if name_col and name_col != 'Author Name':
            rename_dict[name_col] = 'Author Name'
        
        # If no name column was found but we have an email column,
        # assign the first non-email column as Author Name
        if not name_col and email_col:
            for col in df.columns:
                if col != email_col and col not in rename_dict.values():
                    rename_dict[col] = 'Author Name'
                    self.console.print(f"[yellow]Assigned first non-email column as Author Name: {col}[/yellow]")
                    break
        
        # If we have exactly 2 columns and no headers detected,
        # use content analysis to determine which is which
        if len(df.columns) == 2 and not rename_dict:
            email_col = self.detect_email_column_by_content(df)
            if email_col:
                # The other column must be the name column
                other_col = [col for col in df.columns if col != email_col][0]
                rename_dict[email_col] = 'Email'
                rename_dict[other_col] = 'Author Name'
                self.console.print(f"[yellow]Auto-detected columns: {email_col} -> Email, {other_col} -> Author Name[/yellow]")

        # Check if column 3 contains text and assign "Article Title" header
        if len(df.columns) >= 3:
            # Get the third column (index 2)
            third_col = df.columns[2]

            # Check if this column contains text (non-numeric, non-email content)
            if third_col not in rename_dict and third_col != 'Article Title':
                # Sample first 10 non-null values to check for text content
                sample_values = df[third_col].dropna().head(10).astype(str)

                if len(sample_values) > 0:
                    # Check if values contain text (not just numbers or emails)
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    text_count = 0

                    for val in sample_values:
                        val = val.strip()
                        # Skip empty values
                        if not val:
                            continue
                        # Check if it's not just a number and not an email
                        if not val.replace('.', '').replace('-', '').isdigit() and not re.match(email_pattern, val):
                            # Check if it contains alphabetic characters
                            if any(c.isalpha() for c in val):
                                text_count += 1

                    # If more than 50% of sampled values contain text, assign as Article Title
                    if text_count / len(sample_values) > 0.5:
                        rename_dict[third_col] = 'Article Title'
                        self.console.print(f"[yellow]Assigned column 3 as Article Title: {third_col}[/yellow]")

        # Apply renaming
        if rename_dict:
            df = df.rename(columns=rename_dict)
            self.console.print(f"[green]Renamed columns: {rename_dict}[/green]")
        else:
            self.console.print("[yellow]No column renaming needed or could not detect appropriate columns[/yellow]")
        
        return df
    
    def has_proper_headers(self, df):
        """Check if the first row contains proper headers or actual data"""
        if len(df) == 0:
            return True
            
        # Check if any column name looks like an email
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        for col in df.columns:
            if re.match(email_pattern, str(col)):
                return False
        
        # Check if column names are generic (like 'Unnamed: 0', numbers, etc.)
        generic_patterns = [r'^Unnamed:', r'^\d+$', r'^Column\d*$']
        generic_count = 0
        for col in df.columns:
            col_str = str(col)
            if any(re.match(pattern, col_str) for pattern in generic_patterns):
                generic_count += 1
        
        # If most columns are generic, likely no proper headers
        if generic_count / len(df.columns) > 0.5:
            return False
            
        return True

    def split_workbook(self, file_path, rows_per_split=5000):
        """Split an Excel workbook into smaller workbooks"""
        file_path = Path(file_path)
        base_name = file_path.stem
        
        self.console.print(f"\n[blue]Processing: {file_path.name}[/blue]")
        
        try:
            # Read the Excel file
            df = pd.read_excel(file_path)
            
            # Check if file has proper headers
            if not self.has_proper_headers(df):
                self.console.print("[yellow]No proper headers detected. Reading file without headers...[/yellow]")
                # Re-read the file without treating first row as headers
                df = pd.read_excel(file_path, header=None)
                
                # Assign generic column names
                df.columns = [f'Column_{i+1}' for i in range(len(df.columns))]
            
            # Assign standard headers
            df = self.assign_headers(df)
            
            # Check if we have the required columns
            if 'Author Name' not in df.columns or 'Email' not in df.columns:
                self.console.print(f"[red]Warning: Could not find name or email columns in {file_path.name}[/red]")
                self.console.print(f"Available columns: {list(df.columns)}")
                return
            
            # Remove rows with missing emails
            initial_rows = len(df)
            df = df.dropna(subset=['Email'])
            final_rows = len(df)
            
            if initial_rows != final_rows:
                self.console.print(f"[yellow]Removed {initial_rows - final_rows} rows with missing emails[/yellow]")
            
            # Create splits directory
            splits_dir = file_path.parent / "splits"
            splits_dir.mkdir(exist_ok=True)
            
            # Calculate number of splits needed
            total_rows = len(df)
            num_splits = (total_rows + rows_per_split - 1) // rows_per_split
            
            self.console.print(f"[cyan]Total rows: {total_rows:,}[/cyan]")
            self.console.print(f"[cyan]Creating {num_splits} split files with max {rows_per_split:,} rows each[/cyan]")
            
            # Create progress bar
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                
                task = progress.add_task("Creating split files...", total=num_splits)
                
                # Process each split
                for i in range(num_splits):
                    start_idx = i * rows_per_split
                    end_idx = min((i + 1) * rows_per_split, total_rows)

                    # Extract chunk
                    chunk = df.iloc[start_idx:end_idx]

                    # Create output filename with row range
                    start_row = start_idx + 1  # 1-based indexing for display
                    end_row = end_idx
                    output_filename = f"{base_name}_{start_row}-{end_row}.xlsx"
                    output_path = splits_dir / output_filename
                    
                    # Save to Excel
                    chunk.to_excel(output_path, index=False)
                    
                    progress.advance(task)
                    self.console.print(f"[green]Created: {output_filename} ({len(chunk):,} rows)[/green]")
            
            self.console.print(f"[bold green]Successfully split {file_path.name} into {num_splits} files![/bold green]")
            
        except Exception as e:
            self.console.print(f"[red]Error processing {file_path.name}: {str(e)}[/red]")
    
    def process_directory(self, directory_path):
        """Process all Excel files in a directory"""
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            self.console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
            return
        
        # Find all Excel files
        excel_files = list(directory_path.glob("*.xlsx")) + list(directory_path.glob("*.xls"))
        
        if not excel_files:
            self.console.print(f"[red]No Excel files found in: {directory_path}[/red]")
            return
        
        self.print_header(f"Found {len(excel_files)} Excel file(s) to process")
        
        for excel_file in excel_files:
            self.split_workbook(excel_file)
        
        self.console.print(f"\n[bold green]Processing complete! All split files saved in 'splits' directories.[/bold green]")

def main():
    splitter = ExcelHeaderSplitter()
    
    # Use the specified directory path
    directory_path = r"\\DOMINION\mailwizz\mathews mailing\conferences\For Kutools"
    
    splitter.print_header("Excel Header Assignment and Splitting Tool")
    splitter.console.print(f"[cyan]Working directory: {directory_path}[/cyan]")
    
    # Process the directory
    splitter.process_directory(directory_path)

if __name__ == "__main__":
    main()
