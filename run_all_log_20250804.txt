Starting master-s_2.0.py at 04-08-2025 11:52:01.55 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.05s/it]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.05s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:03<00:00,  3.51s/it]
Starting: 100%|##########| 1/1 [00:03<00:00,  3.51s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:09<00:00,  9.33s/it]
Processing: 100%|##########| 1/1 [00:09<00:00,  9.33s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.78s/it]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.78s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 12.23it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.81s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.81s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.35it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.35it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.10s/it]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.10s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:29<00:00, 29.13s/it]
Finishing: 100%|##########| 1/1 [00:29<00:00, 29.13s/it]
SUCCESS: master-s_2.0.py completed successfully at 04-08-2025 11:53:57.09 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 04-08-2025 11:54:18.73 
